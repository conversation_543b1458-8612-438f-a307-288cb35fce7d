import React, { useState, useEffect, useCallback } from 'react';
import { Select, Alert, Form, Button, Row, Card, Spin, message } from 'antd';
import Widget from '../../../../components/Widget';
import FormBuilder from 'antd-form-builder';
import http_utils from '../../../../util/http_utils';
import CircularProgress from '../../../../components/CircularProgress';
import { convertDateFieldsToMoments } from '../../../../util/helpers';
import MetaInputTable from '../../../../components/wify-utils/MetaInputTable';

const protoUrl = '/setup/automation-deployment/execution-master/proto';
const subtmitUrl = '/setup/automation-deployment/execution-master';

const ExecutionMaster = () => {
    const [viewData, setViewData] = useState(undefined);
    const [isLoadingViewData, setIsLoadingViewData] = useState(false);
    const [isFormSubmitting, setIsFormSubmitting] = useState(false);
    const [error, setError] = useState(undefined);
    const [form] = Form.useForm();
    const forceUpdate = FormBuilder.useForceUpdate();
    const [renderHelper, setRenderHelper] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');

    //const [isPrefillProcessed, setIsPrefillProcessed] = useState(false);

    useEffect(() => {
        initViewData();
    }, []);

    // Effect to handle prefill data and trigger brand options fetch (only once)
    useEffect(() => {
        if (viewData && viewData.execution_master_data && !viewData.org_list) {
            const prefillData = viewData.execution_master_data[0].form_data;

            if (prefillData && prefillData.vertical_list) {
                // Set the form values first
                form.setFieldsValue(prefillData);
                // Then fetch brand options for the selected vertical
                fetchBrandOptions(prefillData.vertical_list);
                forceUpdate();
            }
        }
    }, [viewData]);

    // Clear search when brands change
    useEffect(() => {
        setSearchTerm('');
    }, [form.getFieldValue('brand')]);

    // Get all row data without search filter (for preserving data)
    const getAllRowDataForBrandTable = () => {
        console.log('form', form.getFieldValue());
        const selectedBrands = form.getFieldValue('brand') || [];
        if (!selectedBrands.length) return [];

        let rulesJson = form?.getFieldValue('execution_master_table_meta');
        console.log('rulesJson', rulesJson);
        let existingRules;
        if (rulesJson && typeof rulesJson === 'object') {
            existingRules = rulesJson;
        } else if (rulesJson && typeof rulesJson === 'string') {
            existingRules = JSON.parse(rulesJson);
        }
        const orgList = viewData?.org_list || [];
        //if no category wise product list then return empty array
        if (!viewData?.category_wise_product_list) return [];

        // Get saved table data from execution master for prefill
        const savedTableData =
            viewData?.execution_master_data?.[0]?.execution_master_table_meta ||
            [];

        console.log('Saved table data from DB:', savedTableData);
        console.log('Selected brands:', selectedBrands);

        let rowData = [];
        // Filter categories based on selected brands
        const filtered = viewData?.category_wise_product_list.filter(
            (category) => {
                return selectedBrands.includes(category.srvctype_id); // Match the selected service type ID
            }
        );
        //get filtered from the execution master
        console.log('existingRules', existingRules);
        selectedBrands.forEach((brandId) => {
            const brandInfo = orgList.find((org) => org.value === brandId);
            if (!brandInfo) return;

            filtered.forEach((category) => {
                if (category.srvctype_id === brandId) {
                    category.product_details.forEach((product) => {
                        const row_id = `${brandId}_${category.category_id}_${product.product_id}`;

                        // Base row object
                        let rowObj = {
                            row_id,
                            srvc_type: brandId,
                            category_id: category.category_id,
                            category_name: category.category_name,
                            product_id: product.product_id,
                            product_name: product.product_name,
                            sku_code: product.sku_code,
                            product_details: `${product.product_name} (${product.sku_code})`,
                            // Editable defaults
                            skill_1: '',
                            skill_2: '',
                            skill_3: '',
                            manpower_1: '',
                            manpower_2: '',
                            manpower_3: '',
                            duration_1: '',
                            duration_2: '',
                            duration_3: '',
                        };

                        // First check for saved data from database (for prefill)
                        const savedRow = savedTableData.find(
                            (r) => r.row_id === row_id
                        );
                        if (savedRow) {
                            console.log(
                                'Found saved row for',
                                row_id,
                                ':',
                                savedRow
                            );
                            rowObj = { ...rowObj, ...savedRow };
                        }

                        if (existingRules) {
                            let existingRule = existingRules.filter(
                                (singleRule) => singleRule.row_id == row_id
                            )[0];

                            if (existingRule != undefined) {
                                rowObj = { ...rowObj, ...existingRule };
                            }
                        }

                        rowData.push(rowObj);
                    });
                }
            });
        });

        console.log('All row data (no filter):', rowData);
        return rowData;
    };

    // Get filtered row data for display (with search filter)
    const getRowDataForBrandTable = () => {
        let rowData = getAllRowDataForBrandTable();

        // Apply search filter if searchTerm exists
        if (searchTerm && searchTerm.trim()) {
            const searchLower = searchTerm.toLowerCase().trim();
            rowData = rowData.filter((row) => {
                const productNameMatch =
                    row.product_name &&
                    row.product_name.toLowerCase().includes(searchLower);
                const skuCodeMatch =
                    row.sku_code &&
                    row.sku_code.toLowerCase().includes(searchLower);

                return productNameMatch || skuCodeMatch;
            });
        }

        console.log('Filtered row data for display:', rowData);
        return rowData;
    };

    // Get column metadata for brand table
    const getBrandTableColMeta = () => {
        const meta = [
            {
                key: 'srvc_type',
                label: 'Brand/Service Type',
                widget: 'select',
                widgetProps: {
                    disabled: true,
                },
                options: viewData?.org_list || [],
            },
            {
                key: 'product_details',
                label: 'Product Details',
                widget: 'select',
                widgetProps: {
                    disabled: true,
                },
            },
            {
                key: 'skill_1',
                label: 'Skill 1',
                widget: 'select',
                options: viewData?.vertical_skill_list || [],
                widgetProps: {
                    allowClear: true,
                    showSearch: true,
                    optionFilterProp: 'children',
                },
            },
            {
                key: 'manpower_1',
                label: 'Manpower 1',
                widget: 'number',
                widgetProps: {
                    min: 0,
                   
                },
            },
            {
                key: 'duration_1',
                label: 'Duration 1',
                widget: 'number',
                
            },
            {
                key: 'skill_2',
                label: 'Skill 2',
                widget: 'select',
                options: viewData?.vertical_skill_list || [],
                widgetProps: {
                    allowClear: true,
                    showSearch: true,
                    optionFilterProp: 'children',
                },
            },
            {
                key: 'manpower_2',
                label: 'Manpower 2',
                widget: 'number',
                widgetProps: {
                    min: 0,
                   
                },
            },
            {
                key: 'duration_2',
                label: 'Duration 2',
                widget: 'number',
                
            },
            {
                key: 'skill_3',
                label: 'Skill 3',
                widget: 'select',
                options: viewData?.vertical_skill_list || [],
                widgetProps: {
                    allowClear: true,
                    showSearch: true,
                    optionFilterProp: 'children',
                },
            },
            {
                key: 'manpower_3',
                label: 'Manpower 3',
                widget: 'number',
                widgetProps: {
                    min: 0,
                   
                },
            },
            {
                key: 'duration_3',
                label: 'Duration 3',
                widget: 'number',
               
            },
        ];

        return meta;
    };

    // Handle brand table data changes
    const handleBrandTableChange = (newData) => {
        // Get all existing data (including non-filtered rows)
        const allExistingData = getAllRowDataForBrandTable();

        // Create a map of existing data by row_id for quick lookup
        const existingDataMap = {};
        allExistingData.forEach((row) => {
            existingDataMap[row.row_id] = row;
        });

        // Update the map with new data (only the visible/filtered rows)
        newData.forEach((row) => {
            if (row.row_id) {
                existingDataMap[row.row_id] = {
                    ...existingDataMap[row.row_id],
                    ...row,
                };
            }
        });

        // Convert back to array
        const mergedData = Object.values(existingDataMap);

        console.log('Merged data being saved:', mergedData);
        form.setFieldsValue({ execution_master_table_meta: mergedData });
    };

    const fetchBrandOptions = (verticalId) => {
        if (!isLoadingViewData) {
            setIsLoadingViewData(true);
            setError(undefined);
            var params = {};
            const onComplete = (resp) => {
                setIsLoadingViewData(false);
                setViewData(resp.data);
                forceUpdate();
            };
            const onError = (error) => {
                setIsLoadingViewData(false);
                setError(http_utils.decodeErrorToMessage(error));
            };
            http_utils.performGetCall(
                protoUrl + '/' + verticalId,
                params,
                onComplete,
                onError
            );
        }
    };

    const initViewData = () => {
        if (!isLoadingViewData) {
            setIsLoadingViewData(true);
            setViewData(undefined);
            setError(undefined);
            var params = {};
            const onComplete = (resp) => {
                setIsLoadingViewData(false);
                setViewData(resp.data);
            };
            const onError = (error) => {
                setIsLoadingViewData(false);
                setError(http_utils.decodeErrorToMessage(error));
            };
            http_utils.performGetCall(protoUrl, params, onComplete, onError);
        }
    };

    const handleFinish = (values) => {
        if (!isFormSubmitting) {
            setIsFormSubmitting(true);
            var params = values;
            const onComplete = (resp) => {
                setIsFormSubmitting(false);
                message.success('Saved successfully');
            };
            const onError = (error) => {
                setIsFormSubmitting(false);
                message.error(http_utils.decodeErrorToMessage(error));
            };
            http_utils.performPostCall(subtmitUrl, params, onComplete, onError);
        }
    };
    const handleSearch = useCallback((e) => {
        const value = e.target ? e.target.value : e;
        setSearchTerm(value);
    }, []);

    const clearSearch = useCallback(() => {
        setSearchTerm('');
    }, []);

    const getMeta = () => {
        const verticalList = viewData?.vertical_list;
        const selectedVertical = form?.getFieldValue('vertical_list');
        const selectedBrand = form?.getFieldValue('brand');
        const meta = {
            formItemLayout: null, // Must set this for inline layout
            colon: true,
            fields: [
                {
                    key: 'vertical_list',
                    label: 'Select verticals',
                    widget: 'select',
                    widgetProps: {
                        mode: 'single',
                        optionFilterProp: 'children',
                    },
                    onChange: (value) => {
                        console.log('Vertical changed to:', value);

                        forceUpdate();
                        form.setFieldsValue({ brand: undefined });
                        fetchBrandOptions(value);
                    },
                    colSpan: 4,
                    options: verticalList || [],
                },

                ...(selectedVertical
                    ? [
                          {
                              key: 'brand',
                              label: 'Select Brand/Service',
                              widget: 'select',
                              widgetProps: {
                                  mode: 'multiple',
                                  optionFilterProp: 'children',
                                  placeholder: 'Select one or more brands',
                              },
                              options: viewData?.org_list || [],
                              colSpan: 4,
                              onChange: () => {
                                  // Preserve existing form data when brands change
                                  const currentTableData =
                                      form.getFieldValue(
                                          'execution_master_table_meta'
                                      ) || [];
                                  const savedTableData =
                                      viewData?.execution_master_data?.[0]
                                          ?.form_data
                                          ?.execution_master_table_meta || [];

                                  // Merge current form data with saved data, prioritizing current form data
                                  const mergedData = [...currentTableData];
                                  savedTableData.forEach((savedRow) => {
                                      const existingIndex =
                                          mergedData.findIndex(
                                              (row) =>
                                                  row.row_id === savedRow.row_id
                                          );
                                      if (existingIndex === -1) {
                                          mergedData.push(savedRow);
                                      }
                                  });

                                  form.setFieldsValue({
                                      execution_master_table_meta: mergedData,
                                  });

                                  forceUpdate();
                              },
                          },
                      ]
                    : []),
                {
                    key: 'execution_master_table_meta',
                    className: 'gx-d-none',
                    widgetProps: {
                        hidden: true,
                    },
                },
                {
                    //render button for bulk upload
                    key: 'instructions',
                    render: () => (
                        <Button
                            type="primary"
                            style={{ marginBottom: '16px' }}
                            // onClick={() => {
                            //     setIsBulkUploadModalVisible(true);
                            // }}
                        >
                            Bulk Upload
                        </Button>
                    ),
                },

                ...(selectedBrand
                    ? [
                          {
                              key: 'search',
                              widget: 'input',
                              widgetProps: {
                                  placeholder:
                                      'Search by product name, SKU code...',
                                  value: searchTerm,
                                  onChange: handleSearch,
                                  allowClear: true,
                                  onClear: clearSearch,
                              },
                              colSpan: 4,
                          },
                      ]
                    : []),

                ...(selectedBrand
                    ? [
                          {
                              key: 'execution_master_table',
                              render: () => {
                                  return (
                                      <MetaInputTable
                                          colMeta={getBrandTableColMeta()}
                                          rowData={getRowDataForBrandTable()}
                                          pagination={{
                                              pageSize: 10,
                                          }}
                                          noFilters={true}
                                          hideActionBtn={true}
                                          onChange={handleBrandTableChange}
                                      />
                                  );
                              },
                          },
                      ]
                    : []),
            ],
        };

        return meta;
    };

    const meta = getMeta();

    const prefillFormData = convertDateFieldsToMoments(
        viewData?.execution_master_data?.[0]?.form_data || {},
        meta.fields
    );
    return (
        <div>
            {isLoadingViewData ? (
                <div className="gx-loader-view gx-loader-position">
                    <CircularProgress />
                </div>
            ) : viewData == undefined ? (
                <p className="gx-text-red">{error}</p>
            ) : (
                <Widget>
                    <>
                        <h2 className="gx-my-1">Execution Master</h2>
                        <Alert
                            message="Configure execution parameters for your brand-service combinations"
                            type="info"
                            showIcon
                        />
                        <Form
                            form={form}
                            layout="vertical"
                            onFinish={handleFinish}
                            initialValues={prefillFormData}
                        >
                            <FormBuilder meta={meta} form={form} />

                            <Form.Item
                                className="form-footer"
                                style={{ marginTop: 24 }}
                            >
                                {isFormSubmitting && <Spin />}
                                <Button
                                    htmlType="submit"
                                    type="primary"
                                    className="gx-mb-0"
                                    disabled={isFormSubmitting}
                                >
                                    Submit
                                </Button>
                            </Form.Item>
                        </Form>
                    </>
                </Widget>
            )}
        </div>
    );
};

export default ExecutionMaster;
