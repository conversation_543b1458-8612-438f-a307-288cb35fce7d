# EditablePagedApiTable Component

A hybrid React component that combines the search and API functionality of `PagedApiListView` with the editable columns functionality of `MetaInputTable`. This component allows you to display paginated data from an API with search capabilities while also providing inline editing functionality.

## Features

### From PagedApiListView:
- **API Integration**: Fetches data from a specified API endpoint
- **Search Functionality**: Real-time search with debounced API calls
- **Pagination**: Built-in pagination with customizable page sizes
- **Filtering**: Support for column filters and external filter objects
- **Loading States**: Loading indicators during API calls
- **Row Selection**: Optional row selection functionality

### From MetaInputTable:
- **Inline Editing**: Edit table cells directly using various input widgets
- **Dynamic Columns**: Configure columns with metadata including widget types
- **Add/Remove Rows**: Built-in controls to add or remove table rows
- **Form Validation**: Integration with FormBuilder for field validation
- **Multiple Widget Types**: Support for input, select, number, date, and other widgets

## Usage

```jsx
import EditablePagedApiTable from '../components/wify-utils/EditablePagedApiTable';

const MyComponent = () => {
    const [searchQuery, setSearchQuery] = useState('');
    const [filterObject, setFilterObject] = useState({});
    const [tableData, setTableData] = useState([]);

    const colMeta = [
        {
            key: 'name',
            label: 'Name',
            widget: 'input',
            widgetProps: { placeholder: 'Enter name' },
        },
        {
            key: 'status',
            label: 'Status',
            widget: 'select',
            options: [
                { label: 'Active', value: 'active' },
                { label: 'Inactive', value: 'inactive' },
            ],
        },
        // ... more columns
    ];

    return (
        <EditablePagedApiTable
            dataSourceApi="/api/my-data"
            searchQuery={searchQuery}
            filterObject={filterObject}
            colMeta={colMeta}
            edittable={true}
            onDataChange={(data) => setTableData(data)}
            pageSize={10}
        />
    );
};
```

## Props

### API and Search Props (from PagedApiListView)

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `dataSourceApi` | string | required | API endpoint to fetch data from |
| `searchQuery` | string | '' | Search query string |
| `filterObject` | object | {} | Filter object for API calls |
| `extraFilterObject` | object | {} | Additional filters to merge |
| `extraParams` | object | {} | Extra parameters for API calls |
| `pageSize` | number | 10 | Number of items per page |
| `onApiRespChange` | function | - | Callback when API response changes |
| `onTotalUpdated` | function | - | Callback when total count updates |
| `onRowClick` | function | - | Callback when a row is clicked |

### Editable Table Props (from MetaInputTable)

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `colMeta` | array | [] | Column metadata configuration |
| `edittable` | boolean | false | Enable add/remove row functionality |
| `onDataChange` | function | - | Callback when table data changes |
| `readOnly` | boolean | false | Make all fields read-only |
| `hideActionBtn` | boolean | false | Hide add/remove action buttons |
| `actionColFixed` | boolean | false | Fix action column to right |

### Display Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `bordered` | boolean | true | Show table borders |
| `tableSize` | string | 'middle' | Table size: 'small', 'middle', 'large' |
| `sticky` | boolean | false | Enable sticky header |
| `overFlowScrollBar` | boolean | false | Enable horizontal scroll |
| `noFilters` | boolean | false | Disable column filters |
| `paginationSize` | string | 'default' | Pagination size |
| `noTotal` | boolean | false | Hide total count display |
| `demoMode` | boolean | false | Enable demo mode |

## Column Metadata Format

Each column in `colMeta` should follow this structure:

```javascript
{
    key: 'field_name',           // Field key in data object
    label: 'Display Name',       // Column header text
    widget: 'input',             // Widget type: 'input', 'select', 'number', 'date', etc.
    options: [                   // For select widgets
        { label: 'Option 1', value: 'value1' },
        { label: 'Option 2', value: 'value2' },
    ],
    widgetProps: {               // Props passed to the widget
        placeholder: 'Enter value',
        disabled: false,
        min: 0,                  // For number widgets
        // ... other widget-specific props
    },
    disable_filter: false,       // Disable column filter
    dynamicMeta: (record, meta) => { // Dynamic column configuration
        // Return modified meta based on record data
        return { ...meta, widgetProps: { disabled: record.locked } };
    }
}
```

## Widget Types

Supported widget types include:
- `input` - Text input
- `number` - Number input
- `select` - Dropdown select
- `date` - Date picker
- `textarea` - Multi-line text input
- `checkbox` - Checkbox
- `radio` - Radio buttons

## API Response Format

The component expects the API to return data in this format:

```javascript
{
    data: {
        data: [
            { id: 1, name: 'Item 1', status: 'active' },
            { id: 2, name: 'Item 2', status: 'inactive' },
            // ... more items
        ],
        pagination: {
            current: 1,
            pageSize: 10,
            total: 100
        }
    }
}
```

## Events and Callbacks

### onDataChange(newData)
Called whenever table data is modified through inline editing or row add/remove operations.

### onApiRespChange(apiResponse)
Called when a new API response is received.

### onRowClick(rowData)
Called when a table row is clicked.

### onTotalUpdated(total)
Called when the total record count is updated.

## Example Use Cases

1. **User Management**: Display users with search, filter by role/status, and edit user details inline
2. **Inventory Management**: Show products with search, filter by category, and edit quantities/prices
3. **Order Management**: Display orders with search, filter by status, and edit order details
4. **Configuration Tables**: Show settings with search and inline editing capabilities

## Integration with Execution Master

This component can be used to enhance your execution master table by:

1. Adding search functionality to find specific products/SKUs
2. Enabling inline editing of skills, manpower, and duration
3. Providing pagination for large datasets
4. Adding filters for categories or service types

```jsx
// Example for execution master
<EditablePagedApiTable
    dataSourceApi="/api/execution-master"
    searchQuery={searchQuery}
    filterObject={{ vertical_id: selectedVertical }}
    colMeta={executionMasterColMeta}
    edittable={true}
    onDataChange={handleExecutionDataChange}
    pageSize={20}
/>
```

## Notes

- The component automatically adds `input_table_id` and `key` fields to data items for tracking
- Changes are tracked in component state and passed to parent via `onDataChange`
- API calls are debounced to prevent excessive requests during search
- The component handles both controlled and uncontrolled usage patterns
- All MetaInputTable features are preserved while adding API integration
